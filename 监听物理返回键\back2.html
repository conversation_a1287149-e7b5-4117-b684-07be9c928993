<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      // 完全阻止浏览器物理返回键的返回事件
      ;(function () {
        let isBlocking = false

        // 初始化：建立历史记录缓冲区
        function initHistoryBuffer() {
          // 推送多个历史状态作为缓冲区，防止快速连击绕过
          for (let i = 0; i < 10; i++) {
            history.pushState(null, null, document.URL)
          }
        }

        // 强化的popstate事件处理
        function handlePopstate(e) {
          if (isBlocking) return

          isBlocking = true

          // 立即阻止默认行为
          if (e.preventDefault) {
            e.preventDefault()
          }
          if (e.stopPropagation) {
            e.stopPropagation()
          }

          // 使用setTimeout确保在当前事件循环完成后执行
          setTimeout(() => {
            // 推送多个历史状态，确保有足够的缓冲
            for (let i = 0; i < 5; i++) {
              history.pushState(null, null, document.URL)
            }
            isBlocking = false
          }, 0)

          // 立即推送一个状态作为紧急阻止
          history.pushState(null, null, document.URL)

          return false
        }

        // 页面加载完成后立即初始化
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", initHistoryBuffer)
        } else {
          initHistoryBuffer()
        }

        // 绑定popstate事件
        window.addEventListener("popstate", handlePopstate, true)

        // 额外的保护措施：监听beforeunload事件
        window.addEventListener("beforeunload", function (e) {
          // 在页面即将卸载时再次推送历史状态
          history.pushState(null, null, document.URL)
        })

        // 定期维护历史记录缓冲区
        setInterval(() => {
          if (!isBlocking && history.length < 10) {
            history.pushState(null, null, document.URL)
          }
        }, 1000)
      })()

      const app = new Vue({
        el: "#app",
        mounted() {},
        methods: {}
      })
    </script>
  </body>
</html>
