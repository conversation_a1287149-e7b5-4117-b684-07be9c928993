<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>页面2</title>
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <script src="../js/vue.min.js"></script>
  </head>
  <body>
    <div id="app">页面2（彻底阻止返回）</div>

    <script>
      const app = new Vue({
        el: "#app",
        mounted() {
          // 推入两个不同的状态，确保历史栈正常建立
          this.pushFakeHistory()
          this.pushFakeHistory()

          // 拦截返回键
          window.addEventListener("popstate", this.handlePopState)
        },
        methods: {
          pushFakeHistory() {
            // 每次都加不同 hash 保证地址不同
            const fakeHash = "#stop" + Date.now()
            history.pushState({}, "", location.pathname + fakeHash)
          },
          handlePopState() {
            console.log("拦截返回键")
            this.pushFakeHistory() // 回来再压一层
          }
        },
        beforeDestroy() {
          window.removeEventListener("popstate", this.handlePopState)
        }
      })
    </script>
  </body>
</html>
